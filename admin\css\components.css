/* ==========================================================================
   Components - Cards, Buttons, Badges, and UI Elements
   ========================================================================== */

/* Card Components */
.redco-card {
    background: var(--redco-white);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-md);
    padding: var(--redco-space-5);
    margin-bottom: var(--redco-space-5);
    box-shadow: var(--redco-shadow-base);
    transition: box-shadow var(--redco-transition-base);
}

.redco-card:hover {
    box-shadow: var(--redco-shadow-md);
}

.redco-card-header {
    margin-bottom: var(--redco-space-4);
    padding-bottom: var(--redco-space-4);
    border-bottom: 1px solid var(--redco-gray-200);
}

.redco-card-header:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.redco-card-header h2,
.redco-card-header h3,
.redco-card-header h4 {
    margin: 0 0 var(--redco-space-2) 0;
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
}

.redco-card-header h2 {
    font-size: var(--redco-font-size-xl);
}

.redco-card-header h3 {
    font-size: var(--redco-font-size-lg);
}

.redco-card-header h4 {
    font-size: var(--redco-font-size-md);
}

.redco-card-header p {
    margin: 0;
    color: var(--redco-gray-600);
    font-size: var(--redco-font-size-base);
}

.redco-card-body {
    margin-bottom: var(--redco-space-4);
}

.redco-card-body:last-child {
    margin-bottom: 0;
}

.redco-card-footer {
    margin-top: var(--redco-space-4);
    padding-top: var(--redco-space-4);
    border-top: 1px solid var(--redco-gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--redco-space-3);
}

/* Button Components */
.redco-button {
    display: inline-flex;
    align-items: center;
    gap: var(--redco-space-2);
    padding: var(--redco-space-2) var(--redco-space-4);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-base);
    background: var(--redco-white);
    color: var(--redco-primary);
    font-weight: var(--redco-font-weight-medium);
    font-size: var(--redco-font-size-base);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--redco-transition-base);
    white-space: nowrap;
}

.redco-button:hover {
    border-color: var(--redco-primary);
    background: var(--redco-primary-light);
    color: var(--redco-primary);
    text-decoration: none;
}

.redco-button:focus {
    outline: 2px solid var(--redco-primary);
    outline-offset: 2px;
}

.redco-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.redco-button-primary {
    background: var(--redco-primary);
    border-color: var(--redco-primary);
    color: var(--redco-white);
}

.redco-button-primary:hover {
    background: var(--redco-primary-hover);
    border-color: var(--redco-primary-hover);
    color: var(--redco-white);
}

.redco-button-secondary {
    background: var(--redco-gray-100);
    border-color: var(--redco-gray-300);
    color: var(--redco-gray-700);
}

.redco-button-secondary:hover {
    background: var(--redco-gray-200);
    border-color: var(--redco-gray-400);
    color: var(--redco-gray-900);
}

.redco-button-success {
    background: var(--redco-success);
    border-color: var(--redco-success);
    color: var(--redco-white);
}

.redco-button-success:hover {
    background: var(--redco-success-hover);
    border-color: var(--redco-success-hover);
    color: var(--redco-white);
}

.redco-button-warning {
    background: var(--redco-warning);
    border-color: var(--redco-warning);
    color: var(--redco-white);
}

.redco-button-warning:hover {
    background: var(--redco-warning-hover);
    border-color: var(--redco-warning-hover);
    color: var(--redco-white);
}

.redco-button-danger {
    background: var(--redco-error);
    border-color: var(--redco-error);
    color: var(--redco-white);
}

.redco-button-danger:hover {
    background: var(--redco-error-hover);
    border-color: var(--redco-error-hover);
    color: var(--redco-white);
}

/* Button Sizes */
.redco-button-sm {
    padding: var(--redco-space-1) var(--redco-space-3);
    font-size: var(--redco-font-size-sm);
}

.redco-button-lg {
    padding: var(--redco-space-3) var(--redco-space-6);
    font-size: var(--redco-font-size-lg);
}

/* Button Groups */
.redco-button-group {
    display: flex;
    gap: var(--redco-space-2);
}

.redco-button-group .redco-button {
    flex: 1;
}

/* Badge Components */
.redco-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--redco-space-1) var(--redco-space-2);
    border-radius: var(--redco-radius-sm);
    font-size: var(--redco-font-size-xs);
    font-weight: var(--redco-font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-badge-primary {
    background: var(--redco-primary-light);
    color: var(--redco-primary);
}

.redco-badge-success {
    background: var(--redco-success-light);
    color: var(--redco-success);
}

.redco-badge-warning {
    background: var(--redco-warning-light);
    color: var(--redco-warning);
}

.redco-badge-danger {
    background: var(--redco-error-light);
    color: var(--redco-error);
}

.redco-badge-gray {
    background: var(--redco-gray-100);
    color: var(--redco-gray-700);
}

/* Coming Soon Badge */
.redco-coming-soon {
    background: var(--redco-warning-light);
    color: var(--redco-warning);
    padding: 2px 6px;
    border-radius: var(--redco-radius-sm);
    font-size: var(--redco-font-size-xs);
    font-weight: var(--redco-font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: var(--redco-space-2);
}

/* Alert Components */
.redco-alert {
    padding: var(--redco-space-4);
    border-radius: var(--redco-radius-base);
    margin-bottom: var(--redco-space-4);
    border-left: 4px solid;
}

.redco-alert-success {
    background: var(--redco-success-light);
    border-color: var(--redco-success);
    color: var(--redco-success);
}

.redco-alert-warning {
    background: var(--redco-warning-light);
    border-color: var(--redco-warning);
    color: var(--redco-warning);
}

.redco-alert-error {
    background: var(--redco-error-light);
    border-color: var(--redco-error);
    color: var(--redco-error);
}

.redco-alert-info {
    background: var(--redco-primary-light);
    border-color: var(--redco-primary);
    color: var(--redco-primary);
}

/* Success Message Component */
.redco-success-message {
    background: linear-gradient(135deg, var(--redco-success) 0%, var(--redco-success-hover) 100%);
    color: var(--redco-white);
    padding: var(--redco-space-6);
    border-radius: var(--redco-radius-md);
    margin-bottom: var(--redco-space-6);
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: var(--redco-space-4);
}

.redco-success-message h2 {
    margin: 0 0 var(--redco-space-2) 0;
    color: var(--redco-white);
    font-size: var(--redco-font-size-2xl);
    font-weight: var(--redco-font-weight-semibold);
}

.redco-success-message p {
    margin: 0 0 var(--redco-space-2) 0;
    color: rgba(255, 255, 255, 0.9);
    line-height: var(--redco-line-height-normal);
}

.redco-success-message .dashicons {
    font-size: var(--redco-font-size-3xl);
    width: var(--redco-font-size-3xl);
    height: var(--redco-font-size-3xl);
    flex-shrink: 0;
}

/* Loading Spinner */
.redco-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--redco-gray-300);
    border-radius: 50%;
    border-top-color: var(--redco-primary);
    animation: redco-spin 1s ease-in-out infinite;
}

@keyframes redco-spin {
    to {
        transform: rotate(360deg);
    }
}

/* Tooltip Component */
.redco-tooltip {
    position: relative;
    display: inline-block;
}

.redco-tooltip-content {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--redco-gray-900);
    color: var(--redco-white);
    padding: var(--redco-space-2) var(--redco-space-3);
    border-radius: var(--redco-radius-base);
    font-size: var(--redco-font-size-sm);
    white-space: nowrap;
    z-index: var(--redco-z-tooltip);
    transition: all var(--redco-transition-base);
}

.redco-tooltip:hover .redco-tooltip-content {
    visibility: visible;
    opacity: 1;
}

/* Divider Component */
.redco-divider {
    height: 1px;
    background: var(--redco-gray-200);
    margin: var(--redco-space-4) 0;
}

.redco-divider-vertical {
    width: 1px;
    height: 24px;
    background: var(--redco-gray-300);
    margin: 0 var(--redco-space-2);
}
