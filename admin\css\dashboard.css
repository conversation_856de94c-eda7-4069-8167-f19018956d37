/* ==========================================================================
   Dashboard - Dashboard-specific Components and Layouts
   ========================================================================== */

/* Dashboard Stats */
.redco-stats-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--redco-space-4);
    margin: var(--redco-space-4) 0;
}

.redco-stat {
    text-align: center;
    padding: var(--redco-space-3);
    background: var(--redco-gray-50);
    border-radius: var(--redco-radius-base);
    border: 1px solid var(--redco-gray-200);
    transition: all var(--redco-transition-base);
}

.redco-stat:hover {
    background: var(--redco-white);
    box-shadow: var(--redco-shadow-base);
}

.redco-stat-value {
    display: block;
    font-size: var(--redco-font-size-xl);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-primary);
    margin-bottom: var(--redco-space-1);
    line-height: 1;
}

.redco-stat-label {
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--redco-font-weight-medium);
}

/* Action Items */
.redco-action-items {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.redco-action-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--redco-space-4) 0;
    border-bottom: 1px solid var(--redco-gray-100);
    transition: background-color var(--redco-transition-base);
}

.redco-action-item:last-child {
    border-bottom: none;
}

.redco-action-item:hover {
    background: var(--redco-gray-50);
    margin: 0 calc(-1 * var(--redco-space-5));
    padding-left: var(--redco-space-5);
    padding-right: var(--redco-space-5);
    border-radius: var(--redco-radius-base);
}

.redco-action-info {
    flex: 1;
}

.redco-action-info h4 {
    margin: 0 0 var(--redco-space-1) 0;
    font-size: var(--redco-font-size-md);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
}

.redco-action-info p {
    margin: 0;
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-600);
    line-height: var(--redco-line-height-normal);
}

.redco-action-button {
    margin-left: var(--redco-space-3);
    flex-shrink: 0;
}

/* Quick Actions Grid */
.redco-quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--redco-space-4);
    margin: var(--redco-space-4) 0;
}

.redco-quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--redco-space-5);
    background: var(--redco-white);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-md);
    text-decoration: none;
    color: var(--redco-gray-900);
    transition: all var(--redco-transition-base);
}

.redco-quick-action:hover {
    border-color: var(--redco-primary);
    box-shadow: var(--redco-shadow-md);
    text-decoration: none;
    color: var(--redco-gray-900);
}

.redco-quick-action-icon {
    width: var(--redco-space-12);
    height: var(--redco-space-12);
    background: var(--redco-primary-light);
    color: var(--redco-primary);
    border-radius: var(--redco-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--redco-font-size-xl);
    margin-bottom: var(--redco-space-3);
}

.redco-quick-action h4 {
    margin: 0 0 var(--redco-space-2) 0;
    font-size: var(--redco-font-size-md);
    font-weight: var(--redco-font-weight-semibold);
}

.redco-quick-action p {
    margin: 0;
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
    line-height: var(--redco-line-height-normal);
}

/* Performance Metrics */
.redco-performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--redco-space-4);
    margin: var(--redco-space-4) 0;
}

.redco-performance-card {
    background: var(--redco-white);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-md);
    padding: var(--redco-space-4);
    text-align: center;
}

.redco-performance-score {
    font-size: var(--redco-font-size-4xl);
    font-weight: var(--redco-font-weight-bold);
    margin-bottom: var(--redco-space-2);
}

.redco-performance-score.excellent {
    color: var(--redco-success);
}

.redco-performance-score.good {
    color: var(--redco-primary);
}

.redco-performance-score.needs-improvement {
    color: var(--redco-warning);
}

.redco-performance-score.poor {
    color: var(--redco-error);
}

.redco-performance-label {
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-600);
    margin-bottom: var(--redco-space-1);
}

.redco-performance-description {
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-500);
}

/* Recent Activity */
.redco-activity-list {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.redco-activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--redco-space-3);
    padding: var(--redco-space-3) 0;
    border-bottom: 1px solid var(--redco-gray-100);
}

.redco-activity-item:last-child {
    border-bottom: none;
}

.redco-activity-icon {
    width: var(--redco-space-8);
    height: var(--redco-space-8);
    background: var(--redco-gray-100);
    color: var(--redco-gray-600);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--redco-font-size-base);
    flex-shrink: 0;
}

.redco-activity-content {
    flex: 1;
}

.redco-activity-title {
    font-size: var(--redco-font-size-base);
    font-weight: var(--redco-font-weight-medium);
    color: var(--redco-gray-900);
    margin: 0 0 var(--redco-space-1) 0;
}

.redco-activity-description {
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
    margin: 0 0 var(--redco-space-1) 0;
}

.redco-activity-time {
    font-size: var(--redco-font-size-xs);
    color: var(--redco-gray-500);
    margin: 0;
}

/* System Status */
.redco-system-status {
    display: flex;
    flex-direction: column;
    gap: var(--redco-space-3);
}

.redco-status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--redco-space-3);
    background: var(--redco-gray-50);
    border-radius: var(--redco-radius-base);
    border: 1px solid var(--redco-gray-200);
}

.redco-status-info {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
}

.redco-status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.redco-status-icon.active {
    background: var(--redco-success);
}

.redco-status-icon.inactive {
    background: var(--redco-gray-400);
}

.redco-status-icon.error {
    background: var(--redco-error);
}

.redco-status-label {
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-900);
    font-weight: var(--redco-font-weight-medium);
}

.redco-status-value {
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
}

/* Progress Bars */
.redco-progress {
    width: 100%;
    height: 8px;
    background: var(--redco-gray-200);
    border-radius: var(--redco-radius-full);
    overflow: hidden;
    margin: var(--redco-space-2) 0;
}

.redco-progress-bar {
    height: 100%;
    background: var(--redco-primary);
    border-radius: var(--redco-radius-full);
    transition: width var(--redco-transition-slow);
}

.redco-progress-bar.success {
    background: var(--redco-success);
}

.redco-progress-bar.warning {
    background: var(--redco-warning);
}

.redco-progress-bar.error {
    background: var(--redco-error);
}

/* Dashboard Welcome */
.redco-welcome {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-hover) 100%);
    color: var(--redco-white);
    padding: var(--redco-space-8);
    border-radius: var(--redco-radius-md);
    margin-bottom: var(--redco-space-6);
    text-align: center;
}

.redco-welcome h1 {
    margin: 0 0 var(--redco-space-2) 0;
    font-size: var(--redco-font-size-4xl);
    font-weight: var(--redco-font-weight-bold);
    color: var(--redco-white);
}

.redco-welcome p {
    margin: 0 0 var(--redco-space-4) 0;
    font-size: var(--redco-font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.redco-welcome .redco-button {
    background: var(--redco-white);
    color: var(--redco-primary);
    border-color: var(--redco-white);
}

.redco-welcome .redco-button:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.9);
}
