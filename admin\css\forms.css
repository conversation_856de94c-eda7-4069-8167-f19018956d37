/* ==========================================================================
   Forms - Input Fields, Checkboxes, Selects, and Form Layout
   ========================================================================== */

/* Form Container */
.redco-form {
    display: flex;
    flex-direction: column;
    gap: var(--redco-space-4);
}

/* Form Section */
.redco-form-section {
    background: var(--redco-white);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-md);
    padding: var(--redco-space-5);
    margin-bottom: var(--redco-space-5);
}

.redco-form-section-header {
    margin-bottom: var(--redco-space-4);
    padding-bottom: var(--redco-space-3);
    border-bottom: 1px solid var(--redco-gray-200);
}

.redco-form-section-header h3 {
    margin: 0 0 var(--redco-space-1) 0;
    font-size: var(--redco-font-size-lg);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
}

.redco-form-section-header p {
    margin: 0;
    color: var(--redco-gray-600);
    font-size: var(--redco-font-size-base);
}

/* Form Grid Layout */
.redco-form-grid {
    display: grid;
    gap: var(--redco-space-4);
}

.redco-form-grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.redco-form-grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

/* Form Group */
.redco-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--redco-space-2);
}

.redco-form-group-horizontal {
    flex-direction: row;
    align-items: center;
    gap: var(--redco-space-3);
}

/* Form Labels */
.redco-form-label {
    font-weight: var(--redco-font-weight-medium);
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-900);
    margin-bottom: var(--redco-space-1);
    display: block;
}

.redco-form-label-required::after {
    content: " *";
    color: var(--redco-error);
}

/* Form Description */
.redco-form-description {
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
    margin-top: var(--redco-space-1);
    line-height: var(--redco-line-height-normal);
}

/* Input Fields */
.redco-input {
    width: 100%;
    padding: var(--redco-space-2) var(--redco-space-3);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-base);
    font-size: var(--redco-font-size-base);
    font-family: var(--redco-font-family);
    background: var(--redco-white);
    color: var(--redco-gray-900);
    transition: border-color var(--redco-transition-base);
}

.redco-input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px var(--redco-primary-light);
}

.redco-input:disabled {
    background: var(--redco-gray-50);
    color: var(--redco-gray-500);
    cursor: not-allowed;
}

.redco-input.error {
    border-color: var(--redco-error);
}

.redco-input.error:focus {
    box-shadow: 0 0 0 3px var(--redco-error-light);
}

/* Textarea */
.redco-textarea {
    min-height: 100px;
    resize: vertical;
}

/* Select Dropdown */
.redco-select {
    width: 100%;
    padding: var(--redco-space-2) var(--redco-space-3);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-base);
    font-size: var(--redco-font-size-base);
    font-family: var(--redco-font-family);
    background: var(--redco-white);
    color: var(--redco-gray-900);
    cursor: pointer;
    transition: border-color var(--redco-transition-base);
}

.redco-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px var(--redco-primary-light);
}

/* Checkbox Styles */
.redco-checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: var(--redco-space-2);
}

.redco-checkbox {
    width: 16px;
    height: 16px;
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-sm);
    background: var(--redco-white);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.redco-checkbox:checked {
    background: var(--redco-primary);
    border-color: var(--redco-primary);
}

.redco-checkbox:checked::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--redco-white);
    font-size: 10px;
    font-weight: bold;
}

.redco-checkbox:focus {
    outline: 2px solid var(--redco-primary);
    outline-offset: 2px;
}

.redco-checkbox-label {
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-900);
    cursor: pointer;
    line-height: var(--redco-line-height-normal);
}

.redco-checkbox:disabled + .redco-checkbox-label {
    color: var(--redco-gray-500);
    cursor: not-allowed;
}

/* Radio Button Styles */
.redco-radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--redco-space-2);
}

.redco-radio-item {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
}

.redco-radio {
    width: 16px;
    height: 16px;
    border: 1px solid var(--redco-gray-300);
    border-radius: 50%;
    background: var(--redco-white);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
}

.redco-radio:checked {
    border-color: var(--redco-primary);
}

.redco-radio:checked::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--redco-primary);
}

.redco-radio:focus {
    outline: 2px solid var(--redco-primary);
    outline-offset: 2px;
}

.redco-radio-label {
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-900);
    cursor: pointer;
}

/* Toggle Switch */
.redco-toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.redco-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.redco-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--redco-gray-300);
    transition: var(--redco-transition-base);
    border-radius: 24px;
}

.redco-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: var(--redco-white);
    transition: var(--redco-transition-base);
    border-radius: 50%;
}

.redco-toggle input:checked + .redco-toggle-slider {
    background: var(--redco-primary);
}

.redco-toggle input:checked + .redco-toggle-slider:before {
    transform: translateX(20px);
}

.redco-toggle input:focus + .redco-toggle-slider {
    box-shadow: 0 0 0 3px var(--redco-primary-light);
}

/* Form Actions */
.redco-form-actions {
    display: flex;
    gap: var(--redco-space-3);
    padding-top: var(--redco-space-4);
    border-top: 1px solid var(--redco-gray-200);
    margin-top: var(--redco-space-4);
}

.redco-form-actions-right {
    justify-content: flex-end;
}

.redco-form-actions-center {
    justify-content: center;
}

.redco-form-actions-between {
    justify-content: space-between;
}

/* Form Validation */
.redco-form-error {
    color: var(--redco-error);
    font-size: var(--redco-font-size-sm);
    margin-top: var(--redco-space-1);
}

.redco-form-success {
    color: var(--redco-success);
    font-size: var(--redco-font-size-sm);
    margin-top: var(--redco-space-1);
}

/* Input Groups */
.redco-input-group {
    display: flex;
    align-items: stretch;
}

.redco-input-group .redco-input {
    border-radius: 0;
    border-right: none;
}

.redco-input-group .redco-input:first-child {
    border-top-left-radius: var(--redco-radius-base);
    border-bottom-left-radius: var(--redco-radius-base);
}

.redco-input-group .redco-input:last-child {
    border-top-right-radius: var(--redco-radius-base);
    border-bottom-right-radius: var(--redco-radius-base);
    border-right: 1px solid var(--redco-gray-300);
}

.redco-input-addon {
    display: flex;
    align-items: center;
    padding: var(--redco-space-2) var(--redco-space-3);
    background: var(--redco-gray-50);
    border: 1px solid var(--redco-gray-300);
    border-left: none;
    color: var(--redco-gray-600);
    font-size: var(--redco-font-size-base);
}

.redco-input-addon:last-child {
    border-top-right-radius: var(--redco-radius-base);
    border-bottom-right-radius: var(--redco-radius-base);
}
