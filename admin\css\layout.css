/* ==========================================================================
   Layout - Main Structure & Grid System
   ========================================================================== */

/* Reset and Base Styles */
.redco-layout {
    font-family: var(--redco-font-family);
    font-size: var(--redco-font-size-base);
    line-height: var(--redco-line-height-normal);
    color: var(--redco-gray-900);
    background: var(--redco-gray-100);
    margin: 0;
    padding: 0;
}

.redco-layout *,
.redco-layout *::before,
.redco-layout *::after {
    box-sizing: border-box;
}

/* Main Container */
.redco-main-content-full {
    background: var(--redco-gray-100);
    padding: var(--redco-space-5);
    margin: 0 -20px;
    min-height: calc(100vh - 32px);
}

/* Page Header */
.redco-page-header {
    background: var(--redco-white);
    padding: var(--redco-space-6);
    border-radius: var(--redco-radius-md);
    box-shadow: var(--redco-shadow-base);
    margin-bottom: var(--redco-space-5);
    display: flex;
    align-items: center;
    gap: var(--redco-space-4);
    border: 1px solid var(--redco-gray-300);
}

.redco-page-header-icon {
    width: var(--redco-space-12);
    height: var(--redco-space-12);
    background: var(--redco-primary);
    color: var(--redco-white);
    border-radius: var(--redco-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--redco-font-size-xl);
    flex-shrink: 0;
}

.redco-page-header-content {
    flex: 1;
}

.redco-page-header h1 {
    margin: 0 0 var(--redco-space-1) 0;
    font-size: var(--redco-font-size-3xl);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
    line-height: var(--redco-line-height-tight);
}

.redco-page-header p {
    margin: 0;
    color: var(--redco-gray-600);
    font-size: var(--redco-font-size-md);
}

/* Grid System */
.redco-grid {
    display: grid;
    gap: var(--redco-grid-gap);
}

.redco-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.redco-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.redco-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.redco-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.redco-grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.redco-grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Dashboard Specific Layouts */
.redco-dashboard-columns {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--redco-space-6);
    align-items: start;
}

.redco-dashboard-main {
    display: flex;
    flex-direction: column;
    gap: var(--redco-space-5);
}

.redco-dashboard-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--redco-space-5);
}

/* Flexbox Utilities */
.redco-flex {
    display: flex;
}

.redco-flex-col {
    flex-direction: column;
}

.redco-flex-wrap {
    flex-wrap: wrap;
}

.redco-items-center {
    align-items: center;
}

.redco-items-start {
    align-items: flex-start;
}

.redco-items-end {
    align-items: flex-end;
}

.redco-justify-center {
    justify-content: center;
}

.redco-justify-between {
    justify-content: space-between;
}

.redco-justify-start {
    justify-content: flex-start;
}

.redco-justify-end {
    justify-content: flex-end;
}

.redco-flex-1 {
    flex: 1;
}

.redco-flex-auto {
    flex: auto;
}

.redco-flex-none {
    flex: none;
}

/* Spacing Utilities */
.redco-gap-1 { gap: var(--redco-space-1); }
.redco-gap-2 { gap: var(--redco-space-2); }
.redco-gap-3 { gap: var(--redco-space-3); }
.redco-gap-4 { gap: var(--redco-space-4); }
.redco-gap-5 { gap: var(--redco-space-5); }
.redco-gap-6 { gap: var(--redco-space-6); }
.redco-gap-8 { gap: var(--redco-space-8); }

/* Margin Utilities */
.redco-m-0 { margin: 0; }
.redco-m-1 { margin: var(--redco-space-1); }
.redco-m-2 { margin: var(--redco-space-2); }
.redco-m-3 { margin: var(--redco-space-3); }
.redco-m-4 { margin: var(--redco-space-4); }
.redco-m-5 { margin: var(--redco-space-5); }
.redco-m-6 { margin: var(--redco-space-6); }

.redco-mb-0 { margin-bottom: 0; }
.redco-mb-1 { margin-bottom: var(--redco-space-1); }
.redco-mb-2 { margin-bottom: var(--redco-space-2); }
.redco-mb-3 { margin-bottom: var(--redco-space-3); }
.redco-mb-4 { margin-bottom: var(--redco-space-4); }
.redco-mb-5 { margin-bottom: var(--redco-space-5); }
.redco-mb-6 { margin-bottom: var(--redco-space-6); }

.redco-mt-0 { margin-top: 0; }
.redco-mt-1 { margin-top: var(--redco-space-1); }
.redco-mt-2 { margin-top: var(--redco-space-2); }
.redco-mt-3 { margin-top: var(--redco-space-3); }
.redco-mt-4 { margin-top: var(--redco-space-4); }
.redco-mt-5 { margin-top: var(--redco-space-5); }
.redco-mt-6 { margin-top: var(--redco-space-6); }

/* Padding Utilities */
.redco-p-0 { padding: 0; }
.redco-p-1 { padding: var(--redco-space-1); }
.redco-p-2 { padding: var(--redco-space-2); }
.redco-p-3 { padding: var(--redco-space-3); }
.redco-p-4 { padding: var(--redco-space-4); }
.redco-p-5 { padding: var(--redco-space-5); }
.redco-p-6 { padding: var(--redco-space-6); }

/* Text Alignment */
.redco-text-left { text-align: left; }
.redco-text-center { text-align: center; }
.redco-text-right { text-align: right; }

/* Display Utilities */
.redco-block { display: block; }
.redco-inline { display: inline; }
.redco-inline-block { display: inline-block; }
.redco-hidden { display: none; }

/* Position Utilities */
.redco-relative { position: relative; }
.redco-absolute { position: absolute; }
.redco-fixed { position: fixed; }
.redco-sticky { position: sticky; }

/* Width Utilities */
.redco-w-full { width: 100%; }
.redco-w-auto { width: auto; }
.redco-w-fit { width: fit-content; }

/* Height Utilities */
.redco-h-full { height: 100%; }
.redco-h-auto { height: auto; }
.redco-h-fit { height: fit-content; }

/* Overflow Utilities */
.redco-overflow-hidden { overflow: hidden; }
.redco-overflow-auto { overflow: auto; }
.redco-overflow-scroll { overflow: scroll; }
