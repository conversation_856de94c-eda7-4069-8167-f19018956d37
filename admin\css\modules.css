/* ==========================================================================
   Modules - Module Cards, Toggles, and Module-specific Components
   ========================================================================== */

/* Module Grid Layout */
.redco-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--redco-space-5);
    margin: var(--redco-space-5) 0;
}

/* Module Card */
.redco-module-card {
    background: var(--redco-white);
    border: 2px solid var(--redco-gray-200);
    border-radius: var(--redco-radius-md);
    padding: var(--redco-space-5);
    transition: all var(--redco-transition-base);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.redco-module-card:hover {
    border-color: var(--redco-primary);
    box-shadow: var(--redco-shadow-md);
    transform: translateY(-2px);
}

.redco-module-card.enabled {
    border-color: var(--redco-success);
    background: linear-gradient(135deg, var(--redco-white) 0%, var(--redco-success-light) 100%);
}

.redco-module-card.disabled {
    border-color: var(--redco-gray-300);
    background: var(--redco-gray-50);
}

/* Module Card Header */
.redco-module-header {
    display: flex;
    align-items: flex-start;
    gap: var(--redco-space-3);
    margin-bottom: var(--redco-space-4);
}

.redco-module-icon {
    width: var(--redco-space-10);
    height: var(--redco-space-10);
    background: var(--redco-primary-light);
    color: var(--redco-primary);
    border-radius: var(--redco-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--redco-font-size-lg);
    flex-shrink: 0;
}

.redco-module-card.enabled .redco-module-icon {
    background: var(--redco-success-light);
    color: var(--redco-success);
}

.redco-module-card.disabled .redco-module-icon {
    background: var(--redco-gray-200);
    color: var(--redco-gray-500);
}

.redco-module-info {
    flex: 1;
    min-width: 0;
}

.redco-module-title {
    margin: 0 0 var(--redco-space-1) 0;
    font-size: var(--redco-font-size-lg);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
}

.redco-module-card.disabled .redco-module-title {
    color: var(--redco-gray-600);
}

.redco-module-description {
    margin: 0;
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-600);
    line-height: var(--redco-line-height-normal);
}

.redco-module-card.disabled .redco-module-description {
    color: var(--redco-gray-500);
}

/* Module Status Badge */
.redco-module-status {
    position: absolute;
    top: var(--redco-space-3);
    right: var(--redco-space-3);
    padding: var(--redco-space-1) var(--redco-space-2);
    border-radius: var(--redco-radius-full);
    font-size: var(--redco-font-size-xs);
    font-weight: var(--redco-font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-module-status.enabled {
    background: var(--redco-success-light);
    color: var(--redco-success);
}

.redco-module-status.disabled {
    background: var(--redco-gray-200);
    color: var(--redco-gray-600);
}

/* Module Features List */
.redco-module-features {
    list-style: none;
    padding: 0;
    margin: var(--redco-space-3) 0 0 0;
}

.redco-module-features li {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
    padding: var(--redco-space-1) 0;
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
}

.redco-module-features li::before {
    content: "✓";
    color: var(--redco-success);
    font-weight: bold;
    font-size: var(--redco-font-size-xs);
}

.redco-module-card.disabled .redco-module-features li {
    color: var(--redco-gray-500);
}

.redco-module-card.disabled .redco-module-features li::before {
    color: var(--redco-gray-400);
}

/* Module Actions */
.redco-module-actions {
    display: flex;
    gap: var(--redco-space-2);
    margin-top: var(--redco-space-4);
    padding-top: var(--redco-space-3);
    border-top: 1px solid var(--redco-gray-200);
}

.redco-module-card.disabled .redco-module-actions {
    border-color: var(--redco-gray-300);
}

/* Module Settings Panel */
.redco-module-settings {
    background: var(--redco-white);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-md);
    margin-top: var(--redco-space-5);
    overflow: hidden;
}

.redco-module-settings-header {
    background: var(--redco-gray-50);
    padding: var(--redco-space-4);
    border-bottom: 1px solid var(--redco-gray-200);
}

.redco-module-settings-header h3 {
    margin: 0;
    font-size: var(--redco-font-size-lg);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
}

.redco-module-settings-body {
    padding: var(--redco-space-5);
}

/* Module Toggle Switch */
.redco-module-toggle {
    display: flex;
    align-items: center;
    gap: var(--redco-space-3);
    padding: var(--redco-space-4);
    background: var(--redco-gray-50);
    border-radius: var(--redco-radius-base);
    margin-bottom: var(--redco-space-4);
    cursor: pointer;
    transition: all var(--redco-transition-base);
}

.redco-module-toggle:hover {
    background: var(--redco-gray-100);
}

.redco-module-toggle.enabled {
    background: var(--redco-success-light);
}

.redco-module-toggle-switch {
    position: relative;
    width: 48px;
    height: 24px;
    background: var(--redco-gray-300);
    border-radius: 24px;
    transition: background var(--redco-transition-base);
    flex-shrink: 0;
}

.redco-module-toggle.enabled .redco-module-toggle-switch {
    background: var(--redco-success);
}

.redco-module-toggle-switch::after {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: var(--redco-white);
    border-radius: 50%;
    transition: transform var(--redco-transition-base);
}

.redco-module-toggle.enabled .redco-module-toggle-switch::after {
    transform: translateX(24px);
}

.redco-module-toggle-info {
    flex: 1;
}

.redco-module-toggle-title {
    margin: 0 0 var(--redco-space-1) 0;
    font-size: var(--redco-font-size-md);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
}

.redco-module-toggle-description {
    margin: 0;
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
}

/* Module Statistics */
.redco-module-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--redco-space-3);
    margin: var(--redco-space-4) 0;
    padding: var(--redco-space-4);
    background: var(--redco-gray-50);
    border-radius: var(--redco-radius-base);
}

.redco-module-stat {
    text-align: center;
}

.redco-module-stat-value {
    display: block;
    font-size: var(--redco-font-size-lg);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-primary);
    margin-bottom: var(--redco-space-1);
}

.redco-module-stat-label {
    font-size: var(--redco-font-size-xs);
    color: var(--redco-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Module Configuration Sections */
.redco-config-section {
    margin-bottom: var(--redco-space-6);
    padding-bottom: var(--redco-space-4);
    border-bottom: 1px solid var(--redco-gray-200);
}

.redco-config-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.redco-config-section-title {
    margin: 0 0 var(--redco-space-3) 0;
    font-size: var(--redco-font-size-md);
    font-weight: var(--redco-font-weight-semibold);
    color: var(--redco-gray-900);
}

.redco-config-section-description {
    margin: 0 0 var(--redco-space-4) 0;
    font-size: var(--redco-font-size-base);
    color: var(--redco-gray-600);
    line-height: var(--redco-line-height-normal);
}

/* Module Performance Impact */
.redco-performance-impact {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
    margin-top: var(--redco-space-3);
    padding: var(--redco-space-2) var(--redco-space-3);
    background: var(--redco-gray-50);
    border-radius: var(--redco-radius-base);
    font-size: var(--redco-font-size-sm);
}

.redco-performance-impact.low {
    background: var(--redco-success-light);
    color: var(--redco-success);
}

.redco-performance-impact.medium {
    background: var(--redco-warning-light);
    color: var(--redco-warning);
}

.redco-performance-impact.high {
    background: var(--redco-error-light);
    color: var(--redco-error);
}

.redco-performance-impact-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.redco-performance-impact.low .redco-performance-impact-icon {
    background: var(--redco-success);
}

.redco-performance-impact.medium .redco-performance-impact-icon {
    background: var(--redco-warning);
}

.redco-performance-impact.high .redco-performance-impact-icon {
    background: var(--redco-error);
}

/* Module Dependencies */
.redco-module-dependencies {
    margin-top: var(--redco-space-4);
    padding: var(--redco-space-3);
    background: var(--redco-primary-light);
    border-radius: var(--redco-radius-base);
    font-size: var(--redco-font-size-sm);
    color: var(--redco-primary);
}

.redco-module-dependencies strong {
    font-weight: var(--redco-font-weight-semibold);
}
