/* ==========================================================================
   Navigation - Top Navigation Bar & Dropdowns
   ========================================================================== */

/* Top Navigation Container */
.redco-top-nav {
    background: var(--redco-white);
    border-bottom: 1px solid var(--redco-gray-300);
    box-shadow: var(--redco-shadow-base);
    margin: 0 -20px var(--redco-space-5) -20px;
    position: sticky;
    top: 32px;
    z-index: var(--redco-z-sticky);
}

/* Navigation Wrapper */
.redco-nav-wrapper {
    display: flex;
    align-items: center;
    padding: 0 var(--redco-space-6);
    min-height: var(--redco-header-height);
    max-width: var(--redco-container-max-width);
    margin: 0 auto;
}

/* Logo Section */
.redco-nav-logo {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
    margin-right: var(--redco-space-6);
    text-decoration: none;
}

.redco-logo-part-1 {
    color: var(--redco-primary);
    font-weight: var(--redco-font-weight-bold);
    font-size: var(--redco-font-size-xl);
}

.redco-logo-part-2 {
    color: var(--redco-gray-900);
    font-weight: var(--redco-font-weight-normal);
    font-size: var(--redco-font-size-xl);
}

.redco-version {
    background: var(--redco-gray-100);
    color: var(--redco-gray-600);
    padding: 2px 6px;
    border-radius: var(--redco-radius-sm);
    font-size: var(--redco-font-size-xs);
    margin-left: var(--redco-space-2);
}

/* Navigation Items Container */
.redco-nav-items {
    display: flex;
    gap: var(--redco-space-2);
    flex: 1;
    align-items: center;
}

/* Navigation Item Base Styles */
.redco-nav-item,
.redco-nav-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
    padding: var(--redco-space-2) var(--redco-space-4);
    border-radius: var(--redco-radius-base);
    background: transparent;
    color: var(--redco-gray-700);
    text-decoration: none;
    font-weight: var(--redco-font-weight-medium);
    font-size: var(--redco-font-size-base);
    cursor: pointer;
    transition: all var(--redco-transition-base);
    border: none;
    white-space: nowrap;
}

/* Navigation Item Hover States */
.redco-nav-item:hover,
.redco-nav-dropdown-toggle:hover {
    background: var(--redco-gray-50);
    color: var(--redco-gray-900);
    text-decoration: none;
}

/* Active Navigation Item */
.redco-nav-item.active {
    background: var(--redco-primary);
    color: var(--redco-white);
}

.redco-nav-item.active:hover {
    background: var(--redco-primary-hover);
    color: var(--redco-white);
}

/* Navigation Icons */
.redco-nav-item .dashicons,
.redco-nav-dropdown-toggle .dashicons {
    font-size: var(--redco-font-size-md);
    width: var(--redco-font-size-md);
    height: var(--redco-font-size-md);
}

/* Dropdown Container */
.redco-nav-dropdown {
    position: relative;
}

/* Dropdown Toggle Arrow */
.redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    transition: transform var(--redco-transition-base);
    margin-left: var(--redco-space-1);
}

.redco-nav-dropdown.active .redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.redco-nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--redco-white);
    border: 1px solid var(--redco-gray-300);
    border-radius: var(--redco-radius-base);
    box-shadow: var(--redco-shadow-lg);
    min-width: 200px;
    z-index: var(--redco-z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--redco-transition-base);
    padding: var(--redco-space-2);
    margin-top: var(--redco-space-1);
}

/* Dropdown Menu Active State */
.redco-nav-dropdown.active .redco-nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown Menu Items */
.redco-nav-dropdown-menu .redco-nav-item {
    width: 100%;
    justify-content: flex-start;
    margin-bottom: var(--redco-space-1);
    padding: var(--redco-space-2) var(--redco-space-3);
    border-radius: var(--redco-radius-sm);
    font-weight: var(--redco-font-weight-normal);
}

.redco-nav-dropdown-menu .redco-nav-item:last-child {
    margin-bottom: 0;
}

.redco-nav-dropdown-menu .redco-nav-item:hover {
    background: var(--redco-gray-50);
    color: var(--redco-gray-900);
}

.redco-nav-dropdown-menu .redco-nav-item.active {
    background: var(--redco-primary-light);
    color: var(--redco-primary);
}

.redco-nav-dropdown-menu .redco-nav-item.active:hover {
    background: var(--redco-primary-light);
    color: var(--redco-primary);
}

/* Account Section */
.redco-nav-account {
    display: flex;
    align-items: center;
    gap: var(--redco-space-3);
    margin-left: auto;
}

/* License Badge */
.redco-nav-license {
    background: var(--redco-success-light);
    color: var(--redco-success);
    padding: var(--redco-space-1) var(--redco-space-2);
    border-radius: var(--redco-radius-sm);
    font-size: var(--redco-font-size-sm);
    font-weight: var(--redco-font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Version Info */
.redco-nav-version {
    font-size: var(--redco-font-size-xs);
    color: var(--redco-gray-600);
}

/* Navigation Divider */
.redco-nav-divider {
    width: 1px;
    height: 24px;
    background: var(--redco-gray-300);
    margin: 0 var(--redco-space-2);
}

/* Mobile Navigation Toggle (for future use) */
.redco-nav-mobile-toggle {
    display: none;
    background: none;
    border: none;
    padding: var(--redco-space-2);
    cursor: pointer;
    color: var(--redco-gray-700);
}

.redco-nav-mobile-toggle:hover {
    color: var(--redco-gray-900);
}

/* Breadcrumb Navigation (if needed) */
.redco-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--redco-space-2);
    font-size: var(--redco-font-size-sm);
    color: var(--redco-gray-600);
    margin-bottom: var(--redco-space-4);
}

.redco-breadcrumb-item {
    color: var(--redco-gray-600);
    text-decoration: none;
}

.redco-breadcrumb-item:hover {
    color: var(--redco-primary);
}

.redco-breadcrumb-separator {
    color: var(--redco-gray-400);
}

.redco-breadcrumb-current {
    color: var(--redco-gray-900);
    font-weight: var(--redco-font-weight-medium);
}

/* Focus States for Accessibility */
.redco-nav-item:focus,
.redco-nav-dropdown-toggle:focus {
    outline: 2px solid var(--redco-primary);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .redco-nav-item,
    .redco-nav-dropdown-toggle {
        border: 1px solid transparent;
    }
    
    .redco-nav-item:hover,
    .redco-nav-dropdown-toggle:hover {
        border-color: var(--redco-gray-900);
    }
    
    .redco-nav-item.active {
        border-color: var(--redco-white);
    }
}
