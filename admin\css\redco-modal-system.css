/* Modal System CSS */

/* Modal Overlay */
.redco-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.redco-modal-overlay.active {
    display: flex;
    opacity: 1;
    visibility: visible;
}

/* Modal Container */
.redco-modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.redco-modal-overlay.active .redco-modal {
    transform: scale(1);
}

/* Modal Header */
.redco-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.redco-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.redco-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #646970;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.15s ease;
}

.redco-modal-close:hover {
    background: #e0e0e0;
    color: #1d2327;
}

/* Modal Content */
.redco-modal-content {
    padding: 24px;
    max-height: calc(80vh - 140px);
    overflow-y: auto;
}

/* Modal Footer */
.redco-modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.redco-modal-footer .redco-button {
    margin: 0;
}

/* Modal Form Elements */
.redco-modal textarea {
    width: 100%;
    min-height: 200px;
    padding: 12px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    resize: vertical;
}

.redco-modal textarea:focus {
    outline: none;
    border-color: #2271b1;
    box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.1);
}

/* Responsive */
@media (max-width: 600px) {
    .redco-modal {
        width: 95%;
        max-height: 90vh;
    }
    
    .redco-modal-header,
    .redco-modal-content,
    .redco-modal-footer {
        padding: 16px;
    }
    
    .redco-modal-footer {
        flex-direction: column;
        gap: 8px;
    }
    
    .redco-modal-footer .redco-button {
        width: 100%;
        justify-content: center;
    }
}
