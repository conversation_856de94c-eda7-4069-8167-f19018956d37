﻿/* Redco Optimizer Professional CSS */

/* ===== CSS VARIABLES ===== */
:root {
    --primary-color: #2271b1;
    --primary-hover: #135e96;
    --success-color: #00a32a;
    --warning-color: #dba617;
    --error-color: #d63638;
    --text-primary: #1d2327;
    --text-secondary: #646970;
    --text-light: #8c8f94;
    --border-color: #c3c4c7;
    --border-light: #e0e0e0;
    --background-white: #ffffff;
    --background-light: #f6f7f7;
    --background-card: #ffffff;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
    --radius-small: 4px;
    --radius-medium: 8px;
    --radius-large: 12px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --transition-fast: 0.15s ease;
    --transition-medium: 0.3s ease;
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
.redco-layout * {
    box-sizing: border-box;
}

.redco-layout {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: var(--text-primary);
    line-height: 1.5;
}

/* ===== TOP NAVIGATION ===== */
.redco-top-nav {
    background: var(--background-white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 32px;
    z-index: 999;
    margin: 0 -20px 20px -20px;
}

.redco-nav-wrapper {
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-lg);
    min-height: 60px;
    gap: var(--spacing-md);
}

/* Logo Section */
.redco-nav-logo {
    flex-shrink: 0;
    margin-right: var(--spacing-lg);
}

.redco-logo-text {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-xs);
}

.redco-logo-part-1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.redco-logo-part-2 {
    font-size: var(--font-size-xl);
    font-weight: 400;
    color: var(--text-primary);
}

.redco-version {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    background: var(--background-light);
    padding: 2px 6px;
    border-radius: var(--radius-small);
    margin-left: var(--spacing-xs);
}

/* Navigation Items */
.redco-nav-items {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
}

.redco-nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
}

.redco-nav-item:hover {
    background: var(--background-light);
    color: var(--text-primary);
}

.redco-nav-item.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
}

.redco-nav-item .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Dropdown Navigation */
.redco-nav-dropdown {
    position: relative;
}

.redco-nav-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
}

.redco-nav-dropdown-toggle:hover {
    background: var(--background-light);
    color: var(--text-primary);
}

.redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    font-size: 12px;
    transition: transform var(--transition-fast);
}

.redco-nav-dropdown.active .redco-nav-dropdown-toggle .dashicons-arrow-down-alt2 {
    transform: rotate(180deg);
}

.redco-nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-heavy);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-medium);
    padding: var(--spacing-sm);
}

.redco-nav-dropdown.active .redco-nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.redco-nav-dropdown-menu .redco-nav-item {
    width: 100%;
    justify-content: flex-start;
    min-width: auto;
    margin-bottom: var(--spacing-xs);
}

.redco-nav-dropdown-menu .redco-nav-item:last-child {
    margin-bottom: 0;
}

/* Account Section */
.redco-nav-account {
    display: flex;
    align-items: center;
    margin-left: auto;
    margin-right: var(--spacing-lg);
}

.redco-nav-account-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.redco-nav-account-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.redco-nav-license {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--success-color);
    background: rgba(0, 163, 42, 0.1);
    padding: 2px 8px;
    border-radius: var(--radius-small);
}

.redco-nav-version {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.redco-nav-account-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.redco-nav-refresh,
.redco-nav-account-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--radius-medium);
    background: var(--background-light);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.redco-nav-refresh:hover,
.redco-nav-account-link:hover {
    background: var(--primary-color);
    color: white;
}

/* Save Button */
.redco-nav-save {
    flex-shrink: 0;
}

.redco-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    background: var(--background-white);
    color: var(--text-primary);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    min-height: 36px;
}

.redco-button:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.redco-button-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.redco-button-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    color: white;
}

/* ===== PAGE HEADER ===== */
.redco-page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-light);
}

.redco-page-header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.redco-page-header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-medium);
    font-size: 24px;
}

.redco-page-header-content h2 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.redco-page-header-content p {
    margin: 0;
    font-size: var(--font-size-md);
    color: var(--text-secondary);
}

.redco-page-header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ===== MAIN CONTENT LAYOUT ===== */
.redco-main-content-full {
    background: var(--background-light);
    min-height: calc(100vh - 200px);
    padding: var(--spacing-lg);
    margin: 0 -20px;
}

/* ===== TAB CONTENT ===== */
.redco-tab-content {
    display: none;
    background: var(--background-white);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.redco-tab-content.active {
    display: block;
}

/* ===== DASHBOARD CARDS ===== */
.redco-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.redco-dashboard-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-fast);
}

.redco-dashboard-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.redco-dashboard-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.redco-dashboard-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.redco-dashboard-card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--background-light);
    border-radius: var(--radius-medium);
    color: var(--text-secondary);
}

.redco-dashboard-card-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== MODULE CARDS ===== */
.redco-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
}

.redco-module-card {
    background: var(--background-white);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medium);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.redco-module-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.redco-module-card.redco-module-enabled {
    border-color: var(--success-color);
    background: linear-gradient(135deg, var(--background-white) 0%, rgba(0, 163, 42, 0.02) 100%);
}

.redco-module-card.redco-module-disabled {
    border-color: var(--border-light);
    background: var(--background-white);
}

.redco-module-card.redco-module-premium {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, var(--background-white) 0%, rgba(219, 166, 23, 0.02) 100%);
}

.redco-module-card.redco-loading {
    pointer-events: none;
    opacity: 0.7;
}

/* Module Card Icon */
.redco-module-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--background-light);
    border-radius: var(--radius-medium);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
}

.redco-module-enabled .redco-module-icon {
    background: var(--success-color);
    color: white;
}

.redco-module-premium .redco-module-icon {
    background: var(--warning-color);
    color: white;
}

.redco-module-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

/* Module Card Content */
.redco-module-content {
    flex: 1;
}

.redco-module-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: 1.3;
}

.redco-module-description {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0 0 var(--spacing-md) 0;
}

/* Module Status Badge */
.redco-module-status {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
}

.redco-status-badge {
    position: relative;
}

.redco-status-badge::after {
    content: 'Disabled';
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 4px 8px;
    border-radius: var(--radius-small);
    background: var(--text-light);
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-module-enabled .redco-status-badge::after {
    content: 'Enabled';
    background: var(--success-color);
}

.redco-module-premium .redco-status-badge::after {
    content: 'Premium';
    background: var(--warning-color);
}

/* Premium Badge */
.redco-premium-badge {
    display: inline-block;
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 4px 8px;
    background: var(--warning-color);
    color: white;
    border-radius: var(--radius-small);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: var(--spacing-sm);
}

/* Module Actions */
.redco-modules-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding: 0 var(--spacing-lg);
}

.redco-modules-toggle-all {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
}

/* ===== FORM STYLING ===== */
.redco-form {
    padding: var(--spacing-lg);
}

.redco-form-section {
    margin-bottom: var(--spacing-xl);
}

.redco-form-section:last-child {
    margin-bottom: var(--spacing-lg);
}

.redco-section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

/* Form Grid Layout */
.redco-form-grid {
    display: grid;
    gap: var(--spacing-lg);
}

.redco-form-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.redco-form-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Form Groups */
.redco-form-group {
    background: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-medium);
    padding: var(--spacing-lg);
    transition: all var(--transition-fast);
}

.redco-form-group:hover {
    border-color: var(--border-color);
    box-shadow: var(--shadow-light);
}

.redco-form-group-title {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.redco-form-group-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.5;
}

/* Form Controls */
.redco-form-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.redco-form-control:last-child {
    margin-bottom: 0;
}

.redco-form-control label {
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    flex: 1;
}

/* Standard Checkboxes */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* Input Fields */
input[type="text"],
input[type="number"],
input[type="url"],
input[type="email"],
textarea,
select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-small);
    font-size: var(--font-size-md);
    color: var(--text-primary);
    background: var(--background-white);
    transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="url"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.1);
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 80px;
}

/* Select */
select {
    cursor: pointer;
}

/* ===== NOTIFICATIONS & ALERTS ===== */
.redco-notice {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-medium);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid;
}

.redco-notice-success {
    background: rgba(0, 163, 42, 0.1);
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.redco-notice-warning {
    background: rgba(219, 166, 23, 0.1);
    border-left-color: var(--warning-color);
    color: var(--warning-color);
}

.redco-notice-error {
    background: rgba(214, 54, 56, 0.1);
    border-left-color: var(--error-color);
    color: var(--error-color);
}

.redco-notice-info {
    background: rgba(34, 113, 177, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.redco-notice-icon {
    flex-shrink: 0;
    margin-top: 2px;
}

.redco-notice-content {
    flex: 1;
}

.redco-notice-title {
    font-weight: 600;
    margin: 0 0 var(--spacing-xs) 0;
}

.redco-notice-message {
    margin: 0;
    opacity: 0.9;
}

/* ===== LOADING STATES ===== */
.redco-loading {
    position: relative;
    pointer-events: none;
}

.redco-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.redco-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-light);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: redco-spin 1s linear infinite;
    z-index: 11;
}

@keyframes redco-spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== UTILITIES ===== */
.redco-text-center {
    text-align: center;
}

.redco-text-right {
    text-align: right;
}

.redco-text-left {
    text-align: left;
}

.redco-text-primary {
    color: var(--text-primary);
}

.redco-text-secondary {
    color: var(--text-secondary);
}

.redco-text-light {
    color: var(--text-light);
}

.redco-text-success {
    color: var(--success-color);
}

.redco-text-warning {
    color: var(--warning-color);
}

.redco-text-error {
    color: var(--error-color);
}

.redco-bg-white {
    background: var(--background-white);
}

.redco-bg-light {
    background: var(--background-light);
}

.redco-border {
    border: 1px solid var(--border-color);
}

.redco-border-light {
    border: 1px solid var(--border-light);
}

.redco-rounded {
    border-radius: var(--radius-medium);
}

.redco-rounded-small {
    border-radius: var(--radius-small);
}

.redco-rounded-large {
    border-radius: var(--radius-large);
}

.redco-shadow {
    box-shadow: var(--shadow-light);
}

.redco-shadow-medium {
    box-shadow: var(--shadow-medium);
}

.redco-shadow-heavy {
    box-shadow: var(--shadow-heavy);
}

.redco-hidden {
    display: none !important;
}

.redco-visible {
    display: block !important;
}

/* Spacing Utilities */
.redco-m-0 { margin: 0; }
.redco-m-xs { margin: var(--spacing-xs); }
.redco-m-sm { margin: var(--spacing-sm); }
.redco-m-md { margin: var(--spacing-md); }
.redco-m-lg { margin: var(--spacing-lg); }
.redco-m-xl { margin: var(--spacing-xl); }

.redco-mt-0 { margin-top: 0; }
.redco-mt-xs { margin-top: var(--spacing-xs); }
.redco-mt-sm { margin-top: var(--spacing-sm); }
.redco-mt-md { margin-top: var(--spacing-md); }
.redco-mt-lg { margin-top: var(--spacing-lg); }
.redco-mt-xl { margin-top: var(--spacing-xl); }

.redco-mb-0 { margin-bottom: 0; }
.redco-mb-xs { margin-bottom: var(--spacing-xs); }
.redco-mb-sm { margin-bottom: var(--spacing-sm); }
.redco-mb-md { margin-bottom: var(--spacing-md); }
.redco-mb-lg { margin-bottom: var(--spacing-lg); }
.redco-mb-xl { margin-bottom: var(--spacing-xl); }

.redco-p-0 { padding: 0; }
.redco-p-xs { padding: var(--spacing-xs); }
.redco-p-sm { padding: var(--spacing-sm); }
.redco-p-md { padding: var(--spacing-md); }
.redco-p-lg { padding: var(--spacing-lg); }
.redco-p-xl { padding: var(--spacing-xl); }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .redco-nav-wrapper {
        padding: 0 var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .redco-nav-item {
        min-width: 100px;
        padding: var(--spacing-sm);
    }

    .redco-modules-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 900px) {
    .redco-nav-wrapper {
        flex-wrap: wrap;
        min-height: auto;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .redco-nav-items {
        order: 3;
        width: 100%;
        justify-content: center;
        margin-top: var(--spacing-sm);
    }

    .redco-nav-account {
        margin-left: 0;
        margin-right: 0;
    }

    .redco-modules-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: var(--spacing-sm);
    }

    .redco-form-grid-2,
    .redco-form-grid-3 {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 600px) {
    .redco-top-nav {
        margin: 0 -10px 20px -10px;
    }

    .redco-nav-wrapper {
        padding: var(--spacing-sm);
    }

    .redco-nav-items {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .redco-nav-item {
        width: 100%;
        min-width: auto;
        justify-content: center;
    }

    .redco-main-content-full {
        margin: 0 -10px;
        padding: var(--spacing-md);
    }

    .redco-modules-grid {
        grid-template-columns: 1fr;
        padding: var(--spacing-md);
    }

    .redco-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .redco-page-header-right {
        width: 100%;
        justify-content: flex-start;
    }
}

/* ===== PLUGIN NOTICES CONTAINER ===== */
.redco-plugin-notices-container {
    position: relative !important;
    z-index: 1000 !important;
    margin: 0 0 20px 0 !important;
    padding: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.redco-plugin-notices-container .notice,
.redco-plugin-notices-container .redco-notice {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* ===== WORDPRESS COMPATIBILITY ===== */
.wrap .redco-layout {
    margin-top: 0;
}

.wrap h1 {
    margin-bottom: var(--spacing-md);
}

/* Override WordPress admin styles that might conflict */
.redco-layout .button,
.redco-layout .button-primary,
.redco-layout .button-secondary {
    all: unset;
}

.redco-layout .redco-button {
    all: revert;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    background: var(--background-white);
    color: var(--text-primary);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    min-height: 36px;
}

/* ===== ACCESSIBILITY ===== */
.redco-layout *:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.redco-layout button:focus,
.redco-layout .redco-button:focus,
.redco-layout input:focus,
.redco-layout select:focus,
.redco-layout textarea:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.2);
}

/* Screen reader only content */
.redco-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
