/* ==========================================================================
   Responsive - Mobile and Tablet Breakpoints
   ========================================================================== */

/* Large Desktop (1280px and up) */
@media (min-width: 1280px) {
    .redco-nav-wrapper {
        max-width: var(--redco-container-max-width);
        margin: 0 auto;
    }
    
    .redco-main-content-full {
        max-width: calc(var(--redco-container-max-width) + 40px);
        margin: 0 auto;
        padding-left: var(--redco-space-5);
        padding-right: var(--redco-space-5);
    }
}

/* Desktop (1024px to 1279px) */
@media (max-width: 1279px) and (min-width: 1024px) {
    .redco-modules-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .redco-stats-row {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Tablet (768px to 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .redco-nav-wrapper {
        padding: 0 var(--redco-space-4);
    }
    
    .redco-main-content-full {
        padding: var(--redco-space-4);
        margin: 0 -16px;
    }
    
    .redco-page-header {
        padding: var(--redco-space-4);
        flex-direction: column;
        text-align: center;
        gap: var(--redco-space-3);
    }
    
    .redco-page-header-icon {
        width: var(--redco-space-10);
        height: var(--redco-space-10);
    }
    
    .redco-dashboard-columns {
        grid-template-columns: 1fr;
        gap: var(--redco-space-4);
    }
    
    .redco-stats-row {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--redco-space-3);
    }
    
    .redco-modules-grid {
        grid-template-columns: 1fr;
        gap: var(--redco-space-4);
    }
    
    .redco-form-grid-2,
    .redco-form-grid-3 {
        grid-template-columns: 1fr;
    }
    
    .redco-nav-items {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--redco-space-1);
    }
    
    .redco-nav-account {
        order: -1;
        margin-left: 0;
        margin-right: auto;
    }
    
    .redco-quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .redco-performance-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Large (640px to 767px) */
@media (max-width: 767px) and (min-width: 640px) {
    .redco-top-nav {
        margin: 0 -16px var(--redco-space-4) -16px;
    }
    
    .redco-nav-wrapper {
        flex-direction: column;
        padding: var(--redco-space-3);
        min-height: auto;
        gap: var(--redco-space-3);
    }
    
    .redco-nav-logo {
        margin-right: 0;
    }
    
    .redco-nav-items {
        order: 2;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--redco-space-1);
    }
    
    .redco-nav-account {
        order: 1;
        margin-left: 0;
        justify-content: center;
    }
    
    .redco-main-content-full {
        padding: var(--redco-space-3);
        margin: 0 -16px;
    }
    
    .redco-page-header {
        padding: var(--redco-space-4);
        flex-direction: column;
        text-align: center;
        gap: var(--redco-space-3);
    }
    
    .redco-page-header h1 {
        font-size: var(--redco-font-size-2xl);
    }
    
    .redco-dashboard-columns {
        grid-template-columns: 1fr;
        gap: var(--redco-space-4);
    }
    
    .redco-stats-row {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--redco-space-3);
    }
    
    .redco-modules-grid {
        grid-template-columns: 1fr;
        gap: var(--redco-space-4);
    }
    
    .redco-module-card {
        padding: var(--redco-space-4);
    }
    
    .redco-form-section {
        padding: var(--redco-space-4);
    }
    
    .redco-form-grid-2,
    .redco-form-grid-3 {
        grid-template-columns: 1fr;
    }
    
    .redco-form-actions {
        flex-direction: column;
        gap: var(--redco-space-2);
    }
    
    .redco-button-group {
        flex-direction: column;
    }
    
    .redco-quick-actions {
        grid-template-columns: 1fr;
    }
    
    .redco-performance-grid {
        grid-template-columns: 1fr;
    }
    
    .redco-action-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--redco-space-2);
    }
    
    .redco-action-button {
        margin-left: 0;
        align-self: stretch;
    }
}

/* Mobile Small (up to 639px) */
@media (max-width: 639px) {
    .redco-top-nav {
        margin: 0 -16px var(--redco-space-3) -16px;
    }
    
    .redco-nav-wrapper {
        flex-direction: column;
        padding: var(--redco-space-2);
        gap: var(--redco-space-2);
    }
    
    .redco-nav-items {
        flex-direction: column;
        width: 100%;
        gap: var(--redco-space-1);
    }
    
    .redco-nav-item,
    .redco-nav-dropdown-toggle {
        width: 100%;
        justify-content: center;
        padding: var(--redco-space-2);
    }
    
    .redco-nav-dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: transparent;
        padding: 0;
        margin: 0;
    }
    
    .redco-nav-dropdown.active .redco-nav-dropdown-menu {
        display: block;
    }
    
    .redco-nav-dropdown:not(.active) .redco-nav-dropdown-menu {
        display: none;
    }
    
    .redco-main-content-full {
        padding: var(--redco-space-3);
        margin: 0 -16px;
    }
    
    .redco-page-header {
        padding: var(--redco-space-3);
        flex-direction: column;
        text-align: center;
        gap: var(--redco-space-2);
    }
    
    .redco-page-header h1 {
        font-size: var(--redco-font-size-xl);
    }
    
    .redco-page-header-icon {
        width: var(--redco-space-8);
        height: var(--redco-space-8);
        font-size: var(--redco-font-size-md);
    }
    
    .redco-stats-row {
        grid-template-columns: 1fr;
        gap: var(--redco-space-2);
    }
    
    .redco-stat {
        padding: var(--redco-space-2);
    }
    
    .redco-modules-grid {
        grid-template-columns: 1fr;
        gap: var(--redco-space-3);
    }
    
    .redco-module-card {
        padding: var(--redco-space-3);
    }
    
    .redco-module-header {
        flex-direction: column;
        text-align: center;
        gap: var(--redco-space-2);
    }
    
    .redco-module-icon {
        width: var(--redco-space-8);
        height: var(--redco-space-8);
        font-size: var(--redco-font-size-md);
        align-self: center;
    }
    
    .redco-module-actions {
        flex-direction: column;
        gap: var(--redco-space-2);
    }
    
    .redco-form-section {
        padding: var(--redco-space-3);
    }
    
    .redco-form-actions {
        flex-direction: column;
        gap: var(--redco-space-2);
    }
    
    .redco-card {
        padding: var(--redco-space-3);
    }
    
    .redco-card-header {
        padding-bottom: var(--redco-space-2);
        margin-bottom: var(--redco-space-3);
    }
    
    .redco-success-message {
        padding: var(--redco-space-4);
        flex-direction: column;
        text-align: center;
        gap: var(--redco-space-2);
    }
    
    .redco-success-message h2 {
        font-size: var(--redco-font-size-xl);
    }
    
    .redco-quick-actions {
        grid-template-columns: 1fr;
        gap: var(--redco-space-3);
    }
    
    .redco-quick-action {
        padding: var(--redco-space-3);
    }
    
    .redco-performance-grid {
        grid-template-columns: 1fr;
        gap: var(--redco-space-3);
    }
    
    .redco-module-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--redco-space-2);
        padding: var(--redco-space-3);
    }
    
    .redco-action-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--redco-space-2);
        padding: var(--redco-space-3) 0;
    }
    
    .redco-action-button {
        margin-left: 0;
        align-self: stretch;
    }
}

/* Print Styles */
@media print {
    .redco-top-nav,
    .redco-nav-wrapper,
    .redco-button,
    .redco-form-actions {
        display: none !important;
    }
    
    .redco-main-content-full {
        margin: 0;
        padding: 0;
        background: white;
    }
    
    .redco-card,
    .redco-module-card {
        border: 1px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
    
    .redco-page-header {
        border-bottom: 2px solid #000;
        margin-bottom: 20px;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .redco-card,
    .redco-module-card,
    .redco-form-section {
        border-width: 2px;
    }
    
    .redco-button {
        border-width: 2px;
    }
    
    .redco-nav-item,
    .redco-nav-dropdown-toggle {
        border: 1px solid transparent;
    }
    
    .redco-nav-item:hover,
    .redco-nav-dropdown-toggle:hover {
        border-color: currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
