/* ==========================================================================
   CSS Variables - Design System
   ========================================================================== */

:root {
    /* Colors - Primary */
    --redco-primary: #2271b1;
    --redco-primary-hover: #135e96;
    --redco-primary-light: rgba(34, 113, 177, 0.1);
    
    /* Colors - Secondary */
    --redco-secondary: #646970;
    --redco-secondary-light: #f6f7f7;
    
    /* Colors - Success */
    --redco-success: #00a32a;
    --redco-success-hover: #00ba37;
    --redco-success-light: rgba(0, 163, 42, 0.1);
    
    /* Colors - Warning */
    --redco-warning: #dba617;
    --redco-warning-hover: #c9a615;
    --redco-warning-light: rgba(219, 166, 23, 0.1);
    
    /* Colors - Error */
    --redco-error: #d63638;
    --redco-error-hover: #b32d2e;
    --redco-error-light: rgba(214, 54, 56, 0.1);
    
    /* Colors - Neutral */
    --redco-white: #ffffff;
    --redco-black: #1d2327;
    --redco-gray-50: #f9f9f9;
    --redco-gray-100: #f0f0f1;
    --redco-gray-200: #e0e0e0;
    --redco-gray-300: #c3c4c7;
    --redco-gray-400: #a7aaad;
    --redco-gray-500: #8c8f94;
    --redco-gray-600: #646970;
    --redco-gray-700: #50575e;
    --redco-gray-800: #3c434a;
    --redco-gray-900: #1d2327;
    
    /* Typography */
    --redco-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    --redco-font-size-xs: 11px;
    --redco-font-size-sm: 12px;
    --redco-font-size-base: 13px;
    --redco-font-size-md: 14px;
    --redco-font-size-lg: 16px;
    --redco-font-size-xl: 18px;
    --redco-font-size-2xl: 20px;
    --redco-font-size-3xl: 24px;
    --redco-font-size-4xl: 28px;
    
    /* Font Weights */
    --redco-font-weight-normal: 400;
    --redco-font-weight-medium: 500;
    --redco-font-weight-semibold: 600;
    --redco-font-weight-bold: 700;
    
    /* Line Heights */
    --redco-line-height-tight: 1.25;
    --redco-line-height-normal: 1.5;
    --redco-line-height-relaxed: 1.75;
    
    /* Spacing */
    --redco-space-1: 4px;
    --redco-space-2: 8px;
    --redco-space-3: 12px;
    --redco-space-4: 16px;
    --redco-space-5: 20px;
    --redco-space-6: 24px;
    --redco-space-8: 32px;
    --redco-space-10: 40px;
    --redco-space-12: 48px;
    --redco-space-16: 64px;
    --redco-space-20: 80px;
    
    /* Border Radius */
    --redco-radius-sm: 4px;
    --redco-radius-base: 6px;
    --redco-radius-md: 8px;
    --redco-radius-lg: 12px;
    --redco-radius-xl: 16px;
    --redco-radius-full: 9999px;
    
    /* Shadows */
    --redco-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --redco-shadow-base: 0 1px 3px rgba(0, 0, 0, 0.1);
    --redco-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --redco-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --redco-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --redco-transition-fast: 0.15s ease;
    --redco-transition-base: 0.2s ease;
    --redco-transition-slow: 0.3s ease;
    
    /* Z-Index */
    --redco-z-dropdown: 1000;
    --redco-z-sticky: 1020;
    --redco-z-fixed: 1030;
    --redco-z-modal: 1040;
    --redco-z-popover: 1050;
    --redco-z-tooltip: 1060;
    
    /* Layout */
    --redco-container-max-width: 1200px;
    --redco-sidebar-width: 280px;
    --redco-header-height: 60px;
    
    /* Grid */
    --redco-grid-gap: var(--redco-space-6);
    --redco-grid-columns: 12;
    
    /* Breakpoints (for reference in media queries) */
    --redco-breakpoint-sm: 640px;
    --redco-breakpoint-md: 768px;
    --redco-breakpoint-lg: 1024px;
    --redco-breakpoint-xl: 1280px;
    --redco-breakpoint-2xl: 1536px;
}

/* Dark mode variables (if needed in future) */
@media (prefers-color-scheme: dark) {
    :root {
        --redco-bg-primary: #1e1e1e;
        --redco-bg-secondary: #2d2d2d;
        --redco-text-primary: #ffffff;
        --redco-text-secondary: #b3b3b3;
    }
}
